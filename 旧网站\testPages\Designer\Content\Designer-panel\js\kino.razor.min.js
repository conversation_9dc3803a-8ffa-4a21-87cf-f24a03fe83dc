;(function(e){"use strict";var t="@",n=!0,r=function(e,t){if(arguments.length==1)return i(e);var n;return typeof e=="function"?n=e:n=i(e),n.call(null,r.HtmlHelper,t)},i=function(e,t){var n={segments:[],segmentIndex:0,conditionOpeningBraceCount:0},r=u.parse(n,e),i=a.toFunctionContent(r);return new Function("Html","m",i)};r.HtmlHelper={escape:function(e){return(""+e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;").replace(/\//g,"&#x2F;")}};var s={String:0,Variable:1,ScriptBlock:2},o={VariableFirstChar:/^[\(_a-zA-Z]/,Variable:/^(?:(?:\()(?:new\s+)?[_a-zA-Z0-9]+(?:\.|[-\+*\/^=<>?:]|[\[\(][^\]\)]*[\]\)]|[_a-zA-Z0-9]+)*(?:\))|(?:new\s+)?[_a-zA-Z0-9]+(?:\.|[\[\(][^\]\)]*[\]\)]|[_a-zA-Z0-9]+)*)/,ConditionAndLoop:/^(?:if|for|while)\s*\(/,ElseCondition:/^[\s\r\n\t]*else(?:\s*{|[\s\t]+if\()?/},u={parse:function(e,n){var r=n.length;for(var i=0;i<r;i++){var s=n.substr(i,1);if(s===t){this._handleString(e,n,e.segmentIndex,i-e.segmentIndex);var u=n.substr(i+1,1);u===t?(this._handleEscape(e,t,e.segmentIndex),i=e.segmentIndex-1):u==="}"?(this._handleEscape(e,u,e.segmentIndex),i=e.segmentIndex-1):u==="{"?(this._handleScriptBlock(e,n,i+1),i=e.segmentIndex-1):o.VariableFirstChar.test(u)&&(o.ConditionAndLoop.test(n.substr(i+1))?this._handleCondition(e,n,i+1):this._handleVariable(e,n,i+1),i=e.segmentIndex-1)}else s==="}"&&e.conditionOpeningBraceCount>0&&(this._handleCloseBrace(e,n,i),o.ElseCondition.test(n.substr(e.segmentIndex))&&this._handleCondition(e,n,i+1),i=e.segmentIndex-1)}return e.segmentIndex<r&&this._handleString(e,n,e.segmentIndex,r-e.segmentIndex),e.segments},_handleString:function(e,t,n,r){if(r==0)return;e.segments[e.segments.length]={segmentType:s.String,content:t.substr(n,r)},e.segmentIndex=n+r},_handleEscape:function(e,t,n){e.segmentIndex=n+2,e.segments[e.segments.length]={segmentType:s.String,content:t}},_handleVariable:function(e,t,n){var r=t.substr(n),i=o.Variable.exec(r)[0];e.segmentIndex=n+i.length,e.segments[e.segments.length]={segmentType:s.Variable,content:i}},_handleScriptBlock:function(e,t,n){var r=this._getScriptBlockLength(t,n);e.segmentIndex=n+r,e.segments[e.segments.length]={segmentType:s.ScriptBlock,content:t.substr(n+1,r-2)},e.conditionOpeningBraceCount++},_getScriptBlockLength:function(e,t){var n=0;for(var r=t;r<e.length;r++){var i=e.substr(r,1);if(i==="{")n++;else if(i==="}"){if(--n===0)return r-t+1;n--}}throw"no matches found }"},_handleCondition:function(e,t,n){var r=t.substr(n),i=r.indexOf("{");e.segments[e.segments.length]={segmentType:s.ScriptBlock,content:r.substr(0,i+1)},e.segmentIndex=n+i+1,e.conditionOpeningBraceCount++},_handleCloseBrace:function(e,t,n){this._handleString(e,t,e.segmentIndex,n-e.segmentIndex),e.segments[e.segments.length]={segmentType:s.ScriptBlock,content:t.substr(n,1)},e.segmentIndex=n+1,e.conditionOpeningBraceCount--}},a={escape:function(e){return e.replace(/\\/g,"\\\\").replace(/'/g,"\\'").replace(/[\n\r]/g,"")},toFunctionContent:function(e){var t=["var __c=[];with(m||{}){"];for(var r=0;r<e.length;r++)if(e[r].segmentType===s.String)t[t.length]="__c[__c.length] = '"+this.escape(e[r].content)+"';";else if(e[r].segmentType===s.Variable){var i=e[r].content;n?t[t.length]="if(typeof "+i+" !== 'undefined'){__c[__c.length] = "+i+";}":t[t.length]="__c[__c.length] = "+i+";"}else e[r].segmentType===s.ScriptBlock&&(t[t.length]=e[r].content);return t[t.length]="};return __c.join('');",t.join("")}};r.use=function(e){return t=e,this},r.enableEmptyValue=function(e){return n=e,this},typeof module!="undefined"&&module.exports?module.exports=r:(e.kino=e.kino?e.kino:{},e.kino.razor=r)})(window)