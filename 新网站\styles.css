/* 版本7 - 企业官网风格样式 */

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 企业官网配色 */
    --primary-color: #1e40af;
    --primary-dark: #1e3a8a;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    
    /* 背景色 */
    --bg-white: #ffffff;
    --bg-gray-50: #f8fafc;
    --bg-gray-100: #f1f5f9;
    --bg-gray-900: #0f172a;
    --bg-blue-50: #eff6ff;
    
    /* 文字颜色 */
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;
    
    /* 边框颜色 */
    --border-light: #e2e8f0;
    --border-medium: #cbd5e1;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* 动画 */
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
}

/* 基础样式 */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-white);
}

/* 顶部信息栏 */
.top-bar {
    background: var(--bg-gray-900);
    color: var(--text-white);
    padding: 8px 0;
    font-size: 14px;
}

.top-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-left {
    flex: 1;
    text-align: left;
    padding-left: 0;
    margin-left: 0;
}

.welcome-text {
    color: #60a5fa;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    display: block;
    text-align: left;
    margin: 0;
    padding: 0;
}

.top-right {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: nowrap;
}

.top-contact {
    display: inline-block;
    white-space: nowrap;
}

.contact-info {
    color: var(--text-light);
    white-space: nowrap;
}

.contact-info a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-fast);
}

.contact-info a:hover {
    color: var(--text-white);
}

.contact-info i {
    margin-right: 5px;
}

.contact-item {
    display: inline-block;
    color: var(--text-light);
    white-space: nowrap;
    vertical-align: middle;
}

.contact-item i {
    margin-right: 5px;
    vertical-align: middle;
}

.contact-item a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-fast);
    vertical-align: middle;
}

.contact-item a:hover {
    color: var(--text-white);
}

.contact-item span {
    vertical-align: middle;
}

.top-social {
    display: flex;
    gap: 10px;
}

.social-link {
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

/* 微信二维码样式 */
.wechat-link {
    position: relative;
}

.wechat-qr {
    position: fixed;
    top: 60px;
    right: 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 9999;
    min-width: 150px;
    text-align: center;
}

.wechat-qr img {
    width: 120px;
    height: 120px;
    object-fit: contain;
    border-radius: 4px;
    margin-bottom: 8px;
}

.wechat-qr span {
    display: block;
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

.wechat-link:hover .wechat-qr {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 强制顶部联系信息一行显示 */
.top-bar .top-contact .contact-item {
    display: inline-block !important;
    margin-right: 30px;
}

.top-bar .top-contact .contact-item:last-child {
    margin-right: 0;
}

.top-bar .top-contact {
    white-space: nowrap !important;
}

.contact-separator {
    color: var(--text-light);
    margin: 0 15px;
    opacity: 0.5;
}

/* 主导航栏 */
.main-nav {
    background: var(--bg-white);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: 20px;
}

.company-logo {
    height: 50px;
    width: auto;
    object-fit: contain;
}

.logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1;
}

.logo-main {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: 2px;
}

.logo-sub {
    font-size: 12px;
    color: var(--text-muted);
    letter-spacing: 3px;
    margin-top: 2px;
}

.logo-tagline {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
    white-space: nowrap;
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
    flex-wrap: nowrap;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 30px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    border-bottom: 3px solid transparent;
    white-space: nowrap;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* 下拉菜单 */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--bg-white);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1001;
    list-style: none;
    padding: 10px 0;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: 10px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
    white-space: nowrap;
}

.dropdown-menu a:hover {
    background: var(--bg-gray-50);
    color: var(--primary-color);
}

/* 搜索框 */
.nav-search {
    display: flex;
    align-items: center;
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--bg-gray-50);
    border: 1px solid var(--border-light);
    border-radius: 25px;
    overflow: hidden;
    transition: var(--transition-fast);
}

.search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.search-box input {
    border: none;
    background: none;
    padding: 10px 15px;
    outline: none;
    width: 200px;
    font-size: 14px;
}

.search-box button {
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 10px 15px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-box button:hover {
    background: var(--primary-dark);
}

/* 主要内容 */
.main-content {
    margin-top: 0;
}

/* 英雄横幅 */
.hero-banner {
    position: relative;
    height: 100vh;
    min-height: 800px;
    overflow: hidden;
}

.banner-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.banner-slides {
    position: relative;
    width: 100%;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(0, 102, 204, 0.9) 0%,
        rgba(0, 68, 153, 0.95) 30%,
        rgba(0, 51, 102, 0.97) 60%,
        rgba(26, 26, 26, 0.9) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

.bg-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        rgba(0, 102, 204, 0.1) 0%,
        transparent 50%,
        rgba(0, 212, 170, 0.1) 100%);
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.3), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.2), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particles 20s linear infinite;
}

.bg-pattern.pattern-2 {
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
    background-size: 100px 100px;
    animation: slide 15s linear infinite;
}

.bg-pattern.pattern-3 {
    background-image: 
        linear-gradient(90deg, transparent 50%, rgba(255, 255, 255, 0.05) 50%),
        linear-gradient(0deg, transparent 50%, rgba(255, 255, 255, 0.05) 50%);
    background-size: 50px 50px;
    animation: grid 10s linear infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes slide {
    0% { transform: translateX(-100px); }
    100% { transform: translateX(100px); }
}

@keyframes grid {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.slide-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.content-wrapper {
    max-width: 1400px;
    padding: 0 40px;
    text-align: center;
    color: white;
    position: relative;
    z-index: 2;
    animation: fadeInUp 1.2s ease forwards;
}

.slide-title {
    font-size: 4.5rem;
    font-weight: 800;
    margin-bottom: 2rem;
    line-height: 1.1;
    white-space: nowrap;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -1px;
}

.slide-subtitle {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    white-space: nowrap;
    font-weight: 400;
    line-height: 1.6;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.slide-features {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin: 3rem 0;
    padding: 2rem 0;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 180px;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease forwards;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #00d4aa, #0066cc);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.feature-item:hover::before {
    transform: scaleX(1);
}

.feature-item i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.95;
    background: linear-gradient(135deg, #00d4aa, #0066cc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.feature-item:hover i {
    transform: scale(1.2) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(0, 102, 204, 0.3));
}

.feature-item span {
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
}

.slide-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin: 3rem 0;
    padding: 2rem 0;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 3rem 2rem;
    border-radius: 20px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 160px;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease forwards;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #ff6b35, #00d4aa);
    border-radius: 0 0 3px 3px;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-15px) scale(1.08);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.stat-number {
    font-size: 4rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #00d4aa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(0, 212, 170, 0.3);
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

.slide-services {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin: 3rem 0;
    padding: 2rem 0;
}

.service-highlight {
    text-align: center;
    max-width: 240px;
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem 1.5rem;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.service-highlight:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-5px);
}

.service-highlight i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.service-highlight h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.service-highlight p {
    font-size: 0.875rem;
    opacity: 0.9;
    line-height: 1.5;
}

.slide-actions {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    border: 2px solid transparent;
}

.btn-primary {
    background: white;
    color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--bg-gray-100);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
}

/* 轮播控制 */
.banner-controls {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    animation: fadeInUp 1s ease forwards;
    width: 100%;
    margin: 0 auto;
}

.slide-indicators {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.6);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
}

.indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: transparent;
    transition: all 0.3s ease;
}

.indicator.active,
.indicator:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: white;
    transform: scale(1.3);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
    animation: pulse 2s infinite;
}

.indicator.active::before,
.indicator:hover::before {
    background: #0066cc;
}

.slide-arrows {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.arrow {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.arrow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.arrow:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: white;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.arrow:hover::before {
    left: 100%;
}

/* 通用区块样式 */
.section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-tag {
    display: inline-block;
    background: var(--bg-blue-50);
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* 关于我们区域 */
.about-section {
    background: var(--bg-gray-50);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    align-items: start;
}

.text-block {
    margin-bottom: 40px;
}

.text-block h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-primary);
}

.text-block p {
    color: var(--text-secondary);
    line-height: 1.8;
    margin-bottom: 20px;
}

.advantage-list {
    list-style: none;
}

.advantage-list li {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
    color: var(--text-secondary);
}

.advantage-list i {
    color: var(--success-color);
    font-size: 18px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.stat-card {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.stat-info {
    flex: 1;
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: var(--text-secondary);
    font-size: 14px;
}

/* 服务区域 */
.services-section {
    background: var(--bg-white);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.service-card {
    background: white;
    border-radius: 12px;
    padding: 40px 30px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    transition: var(--transition);
    text-align: center;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--bg-blue-50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    color: var(--primary-color);
    font-size: 32px;
    transition: var(--transition);
}

.service-card:hover .service-icon {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.service-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-primary);
}

.service-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 25px;
}

.service-features {
    list-style: none;
    text-align: left;
    margin-bottom: 30px;
}

.service-features li {
    padding: 8px 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 20px;
    font-size: 14px;
}

.service-features li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.service-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition-fast);
}

.service-link:hover {
    color: var(--primary-dark);
}

.service-link i {
    transition: var(--transition-fast);
}

.service-link:hover i {
    transform: translateX(5px);
}

/* 案例区域 */
.cases-section {
    background: var(--bg-gray-50);
}

.cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.case-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.case-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.case-image {
    position: relative;
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-placeholder {
    color: white;
    font-size: 4rem;
    opacity: 0.8;
}

.case-category {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.case-content {
    padding: 30px;
}

.case-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.case-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.case-results {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.result-item {
    background: var(--bg-blue-50);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

.case-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-fast);
}

.case-link:hover {
    color: var(--primary-dark);
}

/* 联系我们区域 */
.contact-section {
    background: var(--bg-white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-card {
    background: var(--bg-gray-50);
    border-radius: 12px;
    padding: 40px;
}

.contact-item {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.contact-details h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.contact-details a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.contact-details a:hover {
    color: var(--primary-dark);
}

.contact-details span {
    color: var(--text-muted);
    font-size: 14px;
}

.form-container {
    background: var(--bg-gray-50);
    border-radius: 12px;
    padding: 40px;
}

.form-container h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.form-container p {
    color: var(--text-secondary);
    margin-bottom: 30px;
}

.form-placeholder {
    background: white;
    border: 2px dashed var(--border-medium);
    border-radius: 12px;
    padding: 60px 40px;
    text-align: center;
    transition: var(--transition);
}

.form-placeholder:hover {
    border-color: var(--primary-color);
    background: var(--bg-blue-50);
}

.placeholder-content i {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.placeholder-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.placeholder-content p {
    color: var(--text-secondary);
    margin-bottom: 30px;
}

.form-btn {
    background: var(--primary-color);
    color: white;
    padding: 15px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition);
}

.form-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 页脚 */
.footer {
    background: var(--bg-gray-900);
    color: var(--text-white);
    padding: 60px 0 20px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.footer-brand .brand-logo {
    display: flex;
    align-items: center;
    gap: 20px;
}

.footer-brand .logo-text {
    display: flex;
    flex-direction: column;
    line-height: 1;
}

.footer-brand .logo-main {
    font-size: 28px;
    font-weight: 700;
    color: white;
    letter-spacing: 2px;
}

.footer-brand .logo-sub {
    font-size: 12px;
    color: var(--text-light);
    letter-spacing: 3px;
    margin-top: 2px;
}

.footer-brand .logo-tagline {
    font-size: 14px;
    color: var(--text-light);
    font-weight: 500;
    white-space: nowrap;
    letter-spacing: 1px;
}

.footer-brand p {
    color: var(--text-light);
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.link-group h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.link-group ul {
    list-style: none;
}

.link-group li {
    margin-bottom: 10px;
}

.link-group a {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition-fast);
}

.link-group a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    font-size: 14px;
}

/* 返回顶部 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* 动画效果 */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes particles {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-200px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 102, 204, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 102, 204, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 102, 204, 0);
    }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .top-container {
        padding: 0 15px;
    }

    .nav-container {
        padding: 0 15px;
    }

    .nav-menu {
        display: none;
    }

    .nav-search {
        display: none;
    }

    .slide-title {
        font-size: 2.5rem;
        white-space: normal;
    }

    .slide-features {
        gap: 2rem;
    }

    .slide-stats {
        gap: 2rem;
    }

    .slide-services {
        gap: 2rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .top-contact {
        gap: 15px;
    }

    .contact-item {
        font-size: 12px;
    }

    .section-container {
        padding: 0 15px;
    }

    section {
        padding: 60px 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .slide-title {
        font-size: 2rem;
        white-space: normal;
    }

    .slide-features {
        flex-direction: column;
        gap: 1rem;
    }

    .slide-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .slide-services {
        flex-direction: column;
        gap: 2rem;
    }

    .slide-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }

    .cases-grid {
        grid-template-columns: 1fr;
    }

    .case-results {
        justify-content: center;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .top-bar {
        display: none;
    }

    .hero-banner {
        height: 500px;
    }

    .slide-title {
        font-size: 1.75rem;
        white-space: normal;
    }

    .slide-subtitle {
        font-size: 1rem;
        white-space: normal;
    }

    .service-card {
        padding: 30px 20px;
    }

    .case-content {
        padding: 20px;
    }

    .contact-card,
    .form-container {
        padding: 30px 20px;
    }

    .form-placeholder {
        padding: 40px 20px;
    }
}
