.w-clearfix:after {
  content: '';
  clear: both;
  height: 0;
  display: block;
  overflow: hidden; }

.w-pageline.w-page-num {
  margin: 20px auto;
  padding: 0;
  font-family: <PERSON><PERSON>,"Lucida Grande","Microsoft Yahei","Hiragino Sans GB","Hiragino Sans GB W3",<PERSON><PERSON><PERSON><PERSON>,STHeiti;
  font-size: 12px;
  display: table; 
  clear: both;
}

.w-pageline.w-page-num ul {
  list-style: none outside none;
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0; }

.w-pageline.w-page-num ul li {
  float: left;
  margin-right: 10px;
  height: 30px;
  line-height: 30px;
  text-align: center; }

.w-pageline.w-page-num ul li em {
  font-style: normal;
  color: #666; }

.w-pageline.w-page-num ul li input {
  padding: 0 10px;
  width: 10px;
  height: 30px;
  line-height: 30px;
  border: 1px solid #ddd;
  border-radius: 3px;
  outline: none; }

.w-pageline.w-page-num ul li a {
  display: block;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
  text-decoration: none; }

.w-pageline.w-page-num ul li a:hover {
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.w-page-cm {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center; }

.w-page-cm.disabled {
  -webkit-opacity: .5;
  -moz-opacity: .5;
  -ms-opacity: .5;
  opacity: .5; }

.w-page-cm.disabled a {
  cursor: default; }

.w-page-square .w-page-cm.disabled a:hover {
  background: #fff;
  color: #666;
  border-color: #ddd; }

.w-page-round .w-page-cm.disabled a:hover {
  background: #fff;
  color: #666;
  border-color: #ddd; }

.w-page-alpha .w-page-cm.disabled a:hover {
  background: #fff;
  color: #666; }

.w-page-square-blue .w-page-cm.disabled a:hover {
  background: #eee;
  color: #666;
  border-color: #eee; }

.w-page-square-red .w-page-cm.disabled a:hover {
  background: #eee;
  color: #666;
  border-color: #eee; }

.w-page-square-orange .w-page-cm.disabled a:hover {
  background: #ddd;
  color: #666;
  border-color: #eee; }

.w-page-square li a i {
  display: none; }

.w-page-square li.w-page-flip {
  width: 67px !important; }

.w-page-square li a {
  color: #666;
  background: #fff;
  border-color: #ddd;
  border-width: 1px;
  border-style: solid;
  border-radius: 3px; }

.w-page-square li a:hover {
  background: #eee;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-page-square li.active a {
  background: #eee;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-page-round li a span {
  display: none; }

.w-page-round li a {
  color: #666;
  background: #fff;
  border-color: #ddd;
  border-width: 1px;
  border-style: solid;
  border-radius: 16px; }

.w-page-round li a:hover {
  background: #eee;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-page-round li.active a {
  background: #eee;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-page-alpha li a span {
  display: none; }

.w-page-alpha li a {
  color: #666;
  background: #fff;
  border-radius: 3px; }

.w-page-alpha li a:hover {
  background: #eee; }

.w-page-alpha li.active a {
  background: #eee; }

.w-page-square-blue li a i {
  display: none; }

.w-page-square-blue li.w-page-flip {
  width: 67px !important; }

.w-page-square-blue li a {
  color: #666;
  background: #eee;
  border-color: #eee;
  border-width: 1px;
  border-style: solid;
  border-radius: 3px; }

.w-page-square-blue li a:hover {
  color: #fff;
  background: #00ACF0;
  border-color: #00ACF0;
  border-width: 1px;
  border-style: solid; }

.w-page-square-blue li.active a {
  color: #fff;
  background: #00ACF0;
  border-color: #00ACF0;
  border-width: 1px;
  border-style: solid; }

.w-page-square-red li a i {
  display: none; }

.w-page-square-red li.w-page-flip {
  width: 67px !important; }

.w-page-square-red li a {
  color: #666;
  background: #eee;
  border-color: #eee;
  border-width: 1px;
  border-style: solid;
  border-radius: 0px; }

.w-page-square-red li a:hover {
  color: #fff;
  background: #EA594F;
  border-color: #EA594F;
  border-width: 1px;
  border-style: solid; }

.w-page-square-red li.active a {
  color: #fff;
  background: #EA594F;
  border-color: #EA594F;
  border-width: 1px;
  border-style: solid; }

.w-page-square-orange li a i {
  display: none; }

.w-page-square-orange li.w-page-flip {
  width: 67px !important; }

.w-page-square-orange li a {
  color: #666;
  background: #ddd;
  border-color: #eee;
  border-width: 1px;
  border-style: solid;
  border-radius: 3px; }

.w-page-square-orange li a:hover {
  color: #fff;
  background: #F57A19;
  border-color: #F57A19;
  border-width: 1px;
  border-style: solid; }

.w-page-square-orange li.active a {
  color: #fff;
  background: #F57A19;
  border-color: #F57A19;
  border-width: 1px;
  border-style: solid; }

.w-clearfix:after {
  content: '';
  clear: both;
  height: 0;
  display: block;
  overflow: hidden; }

.w-pageline.w-page-go {
  display: table;
  margin: 20px auto;
  padding: 0;
  font-family: Arial,"Lucida Grande","Microsoft Yahei","Hiragino Sans GB","Hiragino Sans GB W3",SimSun,STHeiti;
  font-size: 12px; }

.w-pageline.w-page-go ul {
  list-style: none outside none;
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0; }

.w-pageline.w-page-go ul li {
  float: left;
  margin-right: 10px;
  height: 30px;
  line-height: 30px;
  text-align: center; }

.w-pageline.w-page-go ul li em {
  font-style: normal;
  color: #666; }

.w-line-height-ie7 {
  *display: inline-block;
  *height: 30px;
  *line-height: 14px;
  *vertical-align: middle; }

.w-pageline.w-page-go ul li input {
  padding: 0 4px;
  width: 22px;
  height: 30px;
  line-height: 30px;
  border: 1px solid #ddd;
  border-radius: 3px;
  outline: none;
  *display: inline-block;
  text-align: center; }

.w-pageline.w-page-go ul li a {
  display: block;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
  text-decoration: none; }

.w-pageline.w-page-go ul li a:hover {
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.w-pageline.w-page-go .w-page-cm {
  width: 67px;
  height: 30px;
  line-height: 30px;
  text-align: center; }

.w-pageline.w-page-go .w-line-height-ie7 {
  margin-right: 10px; }

.w-pageline.w-page-go .w-page-cm.disabled {
  -webkit-opacity: .5;
  -moz-opacity: .5;
  -ms-opacity: .5;
  opacity: .5; }

.w-pageline.w-page-go .w-page-cm.disabled a {
  cursor: default; }

.w-pageline.w-page-go .w-page-rectangle .w-page-cm.disabled a:hover {
  color: #666;
  background: #eee;
  border-color: #ddd; }

.w-pageline.w-page-go .w-page-rectangle-white .w-page-cm.disabled a:hover {
  color: #666;
  background: #fff;
  border-color: #999; }

.w-pageline.w-page-go .w-page-rectangle-gray .w-page-cm.disabled a:hover {
  color: #666;
  background: #eee;
  border-color: #ddd; }

.w-pageline.w-page-go .w-page-rectangle-red .w-page-cm.disabled a:hover {
  color: #EA594F;
  background: #fff;
  border-color: #EA594F; }

.w-pageline.w-page-go .w-page-rectangle-round .w-page-cm.disabled a:hover {
  color: #666;
  background: #fff;
  border-color: #ddd; }

.w-pageline.w-page-go .w-page-rectangle-alpha .w-page-cm.disabled a:hover {
  color: #666;
  background: none; }

.w-pageline.w-page-go .w-page-rectangle li a i {
  display: none; }

.w-pageline.w-page-go .w-page-rectangle li a {
  color: #666;
  background: #eee;
  border-color: #ddd;
  border-width: 1px;
  border-style: solid;
  border-radius: 3px; }

.w-pageline.w-page-go .w-page-rectangle li a:hover {
  background: #ddd;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle li.active a {
  background: #ddd;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-white li a i {
  display: none; }

.w-pageline.w-page-go .w-page-rectangle-white li a {
  color: #666;
  background: #fff;
  border-color: #999;
  border-width: 1px;
  border-style: solid;
  border-radius: 3px; }

.w-pageline.w-page-go .w-page-rectangle-white li a:hover {
  background: #ccc;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-white li.active a {
  background: #ccc;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-gray li a i {
  display: none; }

.w-pageline.w-page-go .w-page-rectangle-gray li a {
  color: #666;
  background: #eee;
  border-color: #ddd;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-gray li a:hover {
  background: #ddd;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-gray li.active a {
  background: #ddd;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-red li a i {
  display: none; }

.w-pageline.w-page-go .w-page-rectangle-red li input {
  border-radius: 3px !important; }

.w-pageline.w-page-go .w-page-rectangle-red li a {
  color: #EA594F;
  background: #fff;
  border-color: #EA594F;
  border-width: 1px;
  border-style: solid;
  border-radius: 16px; }

.w-pageline.w-page-go .w-page-rectangle-red li a:hover {
  color: #fff;
  background: #EA594F;
  border-color: #EA594F;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-red li.active a {
  color: #fff;
  background: #EA594F;
  border-color: #EA594F;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-round li input {
  width: 67px; }

.w-pageline.w-page-go .w-page-rectangle-round li a span {
  display: none; }

.w-pageline.w-page-go .w-page-rectangle-round li input {
  border-radius: 16px !important; }

.w-pageline.w-page-go .w-page-rectangle-round li.w-page-flip {
  width: 30px !important; }

.w-pageline.w-page-go .w-page-rectangle-round li a {
  color: #666;
  background: #fff;
  border-color: #ddd;
  border-width: 1px;
  border-style: solid;
  border-radius: 16px; }

.w-pageline.w-page-go .w-page-rectangle-round li a:hover {
  color: #666;
  background: #ccc;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-round li.active a {
  color: #666;
  background: #ccc;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-pageline.w-page-go .w-page-rectangle-alpha li a span {
  display: none; }

.w-pageline.w-page-go .w-page-rectangle-alpha li input {
  border-radius: 16px !important; }

.w-pageline.w-page-go .w-page-rectangle-alpha li.w-page-flip {
  width: 30px !important; }

.w-pageline.w-page-go .w-page-rectangle-alpha li a {
  color: #666;
  background: none;
  border-radius: 16px; }

.w-pageline.w-page-go .w-page-rectangle-alpha li input {
  width: 67px; }

.w-pageline.w-page-go .w-page-rectangle-alpha li a:hover {
  color: #666;
  background: #ccc; }

.w-pageline.w-page-go .w-page-rectangle-alpha li.active a {
  color: #666;
  background: #ccc; }

.w-clearfix:after {
  content: '';
  clear: both;
  height: 0;
  display: block;
  overflow: hidden; }

.w-pageline.w-page-more-box {
  margin: 20px 0;
  padding: 0;
  font-family: Arial,"Lucida Grande","Microsoft Yahei","Hiragino Sans GB","Hiragino Sans GB W3",SimSun,STHeiti; }

.w-pageline.w-page-more-box ul {
  list-style: none outside none;
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0; }

.w-pageline.w-page-more-box ul li {
  float: left;
  margin-right: 10px;
  height: 30px;
  line-height: 30px;
  text-align: center; }

.w-pageline.w-page-more-box ul li input {
  width: 10px;
  height: 30px;
  line-height: 30px;
  border: 1px solid #ddd;
  border-radius: 3px; }

.w-pageline.w-page-more-box ul li a {
  display: block;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.w-pageline.w-page-more-box ul li a:hover {
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.w-page-more {
  display: block;
  padding: 0 10px;
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  color: #666;
  text-align: center;
  text-decoration: none; }

.w-page-more:hover {
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear; }

.w-pageline.w-page-more-box a.disabled {
  display: none; }

.w-page-more-one {
  /*background*/
  background-color: #FFF;
  /*border*/
  border-color: #ddd;
  border-width: 1px;
  border-style: solid; }

.w-page-more-one:hover {
  background-color: #EEE;
  /*border*/
  border-color: #ddd;
  border-width: 1px;
  border-style: solid; }

.w-page-more-two {
  /*background*/
  background-color: #EEE;
  /*border*/
  border-color: #EEE;
  border-width: 1px;
  border-style: solid; }

.w-page-more-two:hover {
  background-color: #ddd;
  /*border*/
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-page-more-three {
  background-color: #fff;
  border-color: #EEE;
  border-width: 1px;
  border-style: solid;
  border-radius: 40px; }

.w-page-more-three:hover {
  background-color: #eee;
  border-color: #ddd;
  border-width: 1px;
  border-style: solid; }

.w-page-more-four {
  background-color: #eee;
  border-color: #ddd;
  border-width: 1px;
  border-style: solid; }

.w-page-more-four:hover {
  background-color: #ccc;
  border-color: #ccc;
  border-width: 1px;
  border-style: solid; }

.w-page-more-fives {
  background-color: transparent;
  border-color: transparent;
  border-width: 1px;
  border-style: solid; }

.w-page-more-fives:hover {
  background-color: #ddd; }

.w-page-more-six {
  color: #EA594F;
  background-color: #fff;
  border-color: #EA594F;
  border-width: 1px;
  border-style: solid;
  border-radius: 40px; }

.w-page-more-six:hover {
  color: #fff;
  background-color: #EA594F;
  border-color: #EA594F;
  border-width: 1px;
  border-style: solid; }


