<html>

<head>
    <meta name="viewport" content="width=device-width">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="description">
    <meta name="renderer" content="webkit">
    <meta name="applicable-device" content="pc">
    <meta http-equiv="Cache-Control" content="no-transform">
    <link href="testPages/Designer/Content/bottom/pcstyle.css?_version=20230608105041" rel="stylesheet" type="text/css">
    <link href="testPages/Content/public/css/reset.css?_version=20230608105041" rel="stylesheet" type="text/css">
    <link href="testPages/Designer/Content/base/css/pager.css?_version=20230608105041" rel="stylesheet" type="text/css">
    <link href="testPages/Designer/Content/base/css/hover-effects.css?_version=20230608105041" rel="stylesheet"
        type="text/css">
    <link href="testPages/Designer/Content/base/css/antChain.css?_version=20230608105041" rel="stylesheet"
        type="text/css">
    <link href="testPages/static/iconfont/font_somdr6xou4/iconfont.css" rel="stylesheet" />
    <link
        href="testPages/pubsf/10197/10197361/css/60150_Pc_zh-CN.css?preventCdnCacheSeed=4381bb0dcb0c419db6a82476bc0db30b"
        rel="stylesheet">
    <script src="testPages/Scripts/JQuery/jquery-3.6.3.min.js?_version=20230608105042" type="text/javascript"></script>
    <script src="testPages/Designer/Scripts/jquery.lazyload.min.js?_version=20230608105042"
        type="text/javascript"></script>
    <script src="testPages/Designer/Scripts/smart.animation.min.js?_version=20230608105042"
        type="text/javascript"></script>
    <script src="testPages/Designer/Content/Designer-panel/js/kino.razor.min.js?_version=20230608105041"
        type="text/javascript"></script>
    <script src="testPages/Scripts/common.min.js?v=20200318&amp;_version=20230608105042"
        type="text/javascript"></script>
    <script src="testPages/Administration/Scripts/admin.validator.min.js?_version=20230608105035"
        type="text/javascript"></script>
    <script src="testPages/Administration/Content/plugins/cookie/jquery.cookie.js?_version=20230608105034"
        type="text/javascript"></script>
    <script type="text/javascript" id="jssor-all"
        src="testPages/Designer/Scripts/jssor.slider-22.2.16-all.min.js?_version=20230608105042"></script>
    <script type="text/javascript" id="slideshown"
        src="testPages/Designer/Scripts/slideshow.js?_version=20230608105042"></script>
    <script type="text/javascript" id="lzparallax" src="testPages/static/lzparallax/1.0.0/lz-parallax.min.js"></script>
    <script type="text/javascript" id="SuperSlide"
        src="testPages/Designer/Content/Designer-panel/js/jquery.SuperSlide.2.1.1.js"></script>
    <script type="text/javascript" id="jqPaginator" src="testPages/Scripts/statics/js/jqPaginator.min.js"></script>
    <script type="text/javascript" id="lz-slider" src="testPages/Scripts/statics/js/lz-slider.min.js"></script>
    <script type="text/javascript" id="lz-preview" src="testPages/Scripts/statics/js/lz-preview.min.js"></script>

    <script src="https://map.qq.com/api/gljs?v=1.exp&key=IAMBZ-GQKLC-X6D2H-AMRCD-MA5LZ-DYBYE"></script>

    <title>徐州联创自动化科技有限公司</title>
    <link id="lz-preview-css" href="testPages/Content/css/atlas-preview.css" rel="stylesheet"/>
</head>

<body id="smart-body" area="main">
    <script type="text/javascript">
        $(function () {
            if ("False" == "True") {
                $('#mainContentWrapper').addClass('translate');
                $('#antChainWrap').fadeIn(500);

                $('#closeAntChain').off('click').on('click', function () {
                    $('#antChainWrap').fadeOut('slow', function () {
                        $('#mainContentWrapper').removeClass('translate');
                    });
                    $(document).off("scroll", isWatchScroll);

                });
                $('#showQrcodeBtn').off('click').on('click', function () {
                    $('#qrCodeWrappper').toggleClass('qrCodeShow');
                });
                $(document).scroll(isWatchScroll)
            }


            function isWatchScroll() {
                var scroH = $(document).scrollTop();
                if (scroH >= 80) {
                    $('#mainContentWrapper').removeClass('translate');
                } else {
                    $('#mainContentWrapper').addClass('translate');
                }
            }

            //定义地图中心点坐标
            var center = new TMap.LatLng(34.146317,117.161165);
            //定义map变量，调用 TMap.Map() 构造函数创建地图
            var map = new TMap.Map(document.getElementById('container'), {
                center: center,//设置地图中心点坐标
                zoom: 17.2,   //设置地图缩放级别
                pitch: 43.5,  //设置俯仰角
                rotation: 45,    //设置地图旋转角度
                viewMode: '2D'
            });

            var marker = new TMap.MultiMarker({
                map: map,
                styles: {
                    // 点标记样式
                    marker: new TMap.MarkerStyle({
                    width: 20, // 样式宽
                    height: 30, // 样式高
                    anchor: { x: 10, y: 30 }, // 描点位置
                }),
            },
            geometries: [
                // 点标记数据数组
                {
                    // 标记位置(纬度，经度，高度)
                    position: center,
                    id: 'marker',
                },
                ],
            });


        })
    </script>
    <div id="mainContentWrapper" style="background-color: transparent; background-image: none; background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;
       position: relative; width: 100%;min-width:1200px;background-size: auto;" bgscroll="none">
        <div style="background-color: transparent; background-image: none; background-repeat: no-repeat;background-position:0 0; background:-moz-linear-gradient(top, none, none);background:-webkit-gradient(linear, left top, left bottom, from(none), to(none));background:-o-linear-gradient(top, none, none);background:-ms-linear-gradient(top, none, none);background:linear-gradient(top, none, none);;
           position: relative; width: 100%;min-width:1200px;background-size: auto;" bgscroll="none">
            <div class=" header" cpid="60135" id="smv_Area0"
                style="width: 1200px; height: 70px;  position: relative; margin: 0 auto">
                <div id="smv_tem_1_45" ctype="banner" class="esmartMargin smartAbs smartFixed   " cpid="60135"
                    cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="True" pvid="" tareaid="Area0"
                    re-direction="y" daxis="Y" isdeletable="True"
                    style="height: 70px; width: 100%; left: 0px; top: 0px;right:0px;margin:auto;z-index:13;">
                    <div class="yibuFrameContent tem_1_45  banner_Style1  " style="overflow:visible;;">
                        <div class="fullcolumn-inner smAreaC" id="smc_Area0" cid="tem_1_45" style="width:1200px">
                        </div>
                        <div id="bannerWrap_tem_1_45" class="fullcolumn-outer"
                            style="position: absolute; top: 0px; bottom: 0px; left: 0px; width: 1784px;">
                        </div>

                        <script type="text/javascript">

                            $(function () {
                                var resize = function () {
                                    $("#smv_tem_1_45 >.yibuFrameContent>.fullcolumn-inner").width($("#smv_tem_1_45").parent().width());
                                    $('#bannerWrap_tem_1_45').fullScreen(function (t) {
                                        if (VisitFromMobile()) {
                                            t.css("min-width", t.parent().width())
                                        }
                                    });
                                }
                                if (typeof (LayoutConverter) !== "undefined") {
                                    LayoutConverter.CtrlJsVariableList.push({
                                        CtrlId: "tem_1_45",
                                        ResizeFunc: resize,
                                    });
                                } else {
                                    $(window).resize(function (e) {
                                        if (e.target == this) {
                                            resize();
                                        }
                                    });
                                }

                                resize();
                            });
                        </script>
                    </div>
                </div>
                <div id="smv_tem_2_50" ctype="image" class="esmartMargin smartAbs smartFixed  " cpid="60135"
                    cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0"
                    re-direction="all" daxis="All" isdeletable="True"
                    style="height: 50px; width: 187px; left: 50px; top: 10px;z-index:16;">
                    <div class="yibuFrameContent tem_2_50  image_Style1  " style="overflow:visible;;">
                        <div class="w-image-box image-clip-wrap" data-filltype="0" id="div_tem_2_50"
                            style="height: 50px; width: 187px;">
                            <a target="_self" href="index.html">
                                <img loading="lazy" src="images/LOGO.ico" alt="未标题-1" title="" id="img_smv_tem_2_50"
                                    style="width: 50px; height: 50px;" class="">
                                <label>联创智能</label>
                            </a>
                        </div>

                        <script type="text/javascript">
                            $(function () {

                                InitImageSmv("tem_2_50", "185", "50", "0");

                            });
                        </script>

                    </div>
                </div>
                <div id="smv_tem_28_38" ctype="nav" class="esmartMargin smartAbs smartFixed   " cpid="60135"
                    cstyle="Style7" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0"
                    re-direction="all" daxis="All" isdeletable="True"
                    style="height: 70px; width: 700px; right: 85px; top: 0px;z-index:18;">
                    <div class="yibuFrameContent tem_28_38  nav_Style7  " style="overflow:visible;;">
                        <div id="nav_tem_28_38" class="nav_pc_t_7">
                            <ul class="w-nav" navstyle="style7" style="width:auto;">
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item current">
                                        <a href="index.html" target="_self" class="w-nav-item-link">
                                            <span class="mw-iconfont"></span>
                                            <span class="w-link-txt">首页</span>
                                        </a>

                                    </div>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="smartAI.html" target="_self" class="w-nav-item-link">
                                            <span class="mw-iconfont"></span>
                                            <span class="w-link-txt">AI平台软件</span>
                                        </a>

                                    </div>
                                    <ul class="w-subnav"
                                        style="width: 220px; display: none; height: 400px; padding-top: 0px; margin-top: 0px; padding-bottom: 0px; margin-bottom: 0px;">
                                        <li class="w-subnav-item">
                                            <a href="smartAI_CM.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">采煤工作面智能监察系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_JJ.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">掘进工作面智能监察系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_CNL.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">超能力生产智能分析系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_CDY.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">超定员智能分析系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_JXGJDD.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">井下关键地点视频智能分析系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_DDSKG.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">调度室空岗、睡岗智能分析系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_YJZH.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">应急指挥通讯系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_MKXJRY.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">煤矿下井人员、设备精准定位系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_FZYS.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">辅助运输智能检测系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="smartAI_JSYTSF.html"
                                                target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">一通三防智能检测系统</span>
                                            </a>
                                        </li>


                                    </ul>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="kyList.html" target="_self" class="w-nav-item-link">
                                            <span class="mw-iconfont"></span>
                                            <span class="w-link-txt">智能硬件</span>
                                        </a>
                                    </div>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="sovleWay.html" target="_self" class="w-nav-item-link">
                                            <span class="mw-iconfont"></span>
                                            <span class="w-link-txt">系统解决方案</span>
                                        </a>
                                    </div>
                                    <ul class="w-subnav"
                                        style="width: 190px; display: none; height: 240px; padding-top: 0px; margin-top: 0px; padding-bottom: 0px; margin-bottom: 0px;">
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_181_4" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">综合自动化管理平台</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_182_20" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">变电所自动化系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_183_34" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">运输自动化系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_184_52" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">排水自动化系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_185_5" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">通风机自动化系统</span>
                                            </a>

                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_186_19" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">压风自动化系统</span>
                                            </a>
                                        </li>
                                        <li class="w-subnav-item">
                                            <a href="sovleWay.html#smv_con_186_19" target="_self" class="w-subnav-link"
                                                style="height:40px;line-height:40px">
                                                <span class="mw-iconfont"></span>
                                                <span class="w-link-txt">万兆环网集控系统</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="supportService.html" target="_self" class="w-nav-item-link">
                                            <span class="mw-iconfont"></span>
                                            <span class="w-link-txt">支持服务</span>
                                        </a>
                                    </div>
                                </li>
                                <li class="w-nav-inner" style="height:70px;line-height:70px;">
                                    <div class="w-nav-item">
                                        <a href="aboutUs.html" target="_self" class="w-nav-item-link">
                                            <span class="mw-iconfont"></span>
                                            <span class="w-link-txt">关于我们</span>
                                        </a>

                                    </div>
                                </li>

                            </ul>
                        </div>
                        <script>
                            $(function () {
                                $('#nav_tem_28_38 .w-nav').find('.w-subnav').hide();
                                var $this, item, itemAll;

                                if ("False".toLocaleLowerCase() == "true") {
                                } else {
                                    //$("#nav_tem_28_38 .w-subnav").css("width", "190" + "px");
                                }

                                $('#nav_tem_28_38 .w-nav').off('mouseenter').on('mouseenter', '.w-nav-inner', function () {
                                    itemAll = $('#nav_tem_28_38 .w-nav').find('.w-subnav');
                                    $this = $(this);
                                    item = $this.find('.w-subnav');
                                    item.slideDown();
                                }).off('mouseleave').on('mouseleave', '.w-nav-inner', function () {
                                    item = $(this).find('.w-subnav');
                                    item.stop().slideUp();
                                });
                                SetNavSelectedStyle('nav_tem_28_38');//选中当前导航
                            });
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--展示区域-->
    <div class="container" style="display: block;width: 100%;height: 92%;">
        <!--左部分-->
        <div class="container_left" style="display: block;width: 20%;height: 100%;float: left;background-color: #56abe2;color: white;"> 
            <p style="margin: 3% 0 3% 2%;"><label style="font-size: 25px;">公司团队：</label></p>
            <p style="font-size: 20px;line-height: 30px;margin:0 0 3% 2%;text-indent: 2em;">团队共有68人，其中教授、研究员7人，副教授、高级工程师4人，在校博士研究生8人，硕士研究生32人。</p>
            <ul style="font-size: 18px;line-height: 50px;margin-left: 2%;">
                <li><i class="iconfont icon-arrow-right"></i>江苏省"六大人才高峰"3人</li>
                <li><i class="iconfont icon-arrow-right"></i>全国煤炭行业教育先进工作者1人</li>
                <li><i class="iconfont icon-arrow-right"></i>科技部"科技创新人才"2人</li>
                <li><i class="iconfont icon-arrow-right"></i>江苏省"333人才培养工程"3人</li>
                <li style="line-height: 22px;margin: 3% 0 2% 0;"><i class="iconfont icon-arrow-right"></i>江苏省高校"青蓝工程"中青年学术带头人2人</li>
                <li style="line-height: 22px;margin: 3% 0 2% 0;"><i class="iconfont icon-arrow-right"></i>国家高层次人才特殊支持计划(万人计划)科技创业领军人才1人</li>
            </ul>
            <p style="margin: 12% 0 3% 2%;"><label style="font-size: 25px;">联系我们：</label></p>
            <ul style="font-size: 18px;line-height: 50px;margin: 0 0 0 2%;">
                <li>名称：徐州联创自动化科技有限公司</li>
                <li style="line-height: 30px;">地址：徐州高新技术产业开发区漓江路15号徐州国家安全科技产业园B5#-1-205</li>
                <li>电话：李经理14752221901</li>
                <li>邮箱：<EMAIL></li>
            </ul>
        </div>

        <!--右部分-->
        <div class="container_right" style="display: block; width: 80%;height: 100%;float: left;">
            <!-- <img src="images/location.png" style="width: auto;height: 100%"> -->
            <div id="container"></div>
        </div>
    </div>

    <script type="text/javascript">
        $(document.body).bind('contextmenu', function () { return false; });
    </script>

    <div id="smv_tem_13_34" ctype="qqservice" class="esmartMargin smartAbs smartFixed    exist" cpid="60135"
        cstyle="Style1" ccolor="Item0" areaid="Area0" iscontainer="False" pvid="" tareaid="Area0" re-direction="x"
        daxis="All" isdeletable="True" style="height: 61px; width: 100px; right: 0px; bottom: 0px;z-index:6;">
        <div class="yibuFrameContent tem_13_34  qqservice_Style1  " style="overflow:hidden;;">
            <script>

                $(function () {
                    var sv = $("#qqservice_tem_13_34");

                    var numbers = [];
                    $.each(sv.find(".w-cs-menu"), function () { numbers.push(this.scrollWidth); });
                    var maxInNumbers = Math.max.apply(Math, numbers);

                    sv.find(".w-cs-menu").css("width", maxInNumbers + "px");
                    //  显示
                    sv.find(".w-cs-list").hover(function () {
                        $(this).find("ul.w-cs-menu").stop().animate({ right: 61 }, 200);
                    }, function () {
                        $(this).find("ul.w-cs-menu").stop().animate({ right: "0" }, 200);
                    });

                    $("#smv_tem_13_34").addClass('exist').appendTo($('body'));

                });
                function gotoTop(acceleration, stime) {
                    acceleration = acceleration || 0.1;
                    stime = stime || 10;
                    var x1 = 0;
                    var y1 = 0;
                    var x2 = 0;
                    var y2 = 0;
                    if (document.documentElement) {
                        x1 = document.documentElement.scrollLeft || 0;
                        y1 = document.documentElement.scrollTop || 0;
                    }
                    if (document.body) {
                        x2 = document.body.scrollLeft || 0;
                        y2 = document.body.scrollTop || 0;
                    }
                    var x3 = window.scrollX || 0;
                    var y3 = window.scrollY || 0;

                    // 滚动条到页面顶部的水平距离
                    var x = Math.max(x1, Math.max(x2, x3));
                    // 滚动条到页面顶部的垂直距离
                    var y = Math.max(y1, Math.max(y2, y3));

                    // 滚动距离 = 目前距离 / 速度, 因为距离原来越小, 速度是大于 1 的数, 所以滚动距离会越来越小
                    var speeding = 1 + acceleration;
                    window.scrollTo(Math.floor(x / speeding), Math.floor(y / speeding));

                    // 如果距离不为零, 继续调用函数
                    if (x > 0 || y > 0) {
                        var run = "gotoTop(" + acceleration + ", " + stime + ")";
                        window.setTimeout(run, stime);
                    }

                    if (typeof (LayoutConverter) !== "undefined" && typeof (CtrlAdjuster) !== "undefined" && CtrlAdjuster.IsMobile) {
                        $("#qqservice_tem_13_34").trigger("mouseout");
                    }
                }
            </script>
        </div>
    </div>
</body>

</html>