.lz-atlas-preview {
  position: fixed;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 99999999;
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  background: #222 \0;
  *background: #222; }

.lz-atlas-preview img {
  border: none; }

.lz-atlas-preview .lz-atlas-ul {
  list-style: none;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: -14px;
  padding-top: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
  overflow: hidden; }

.lz-atlas-preview .lz-atlas-ul li {
  float: left;
  *margin-bottom: 14px; }

.lz-atlas-preview .lz-atlas-ul li a {
  text-decoration: none;
  float: left;
  margin-bottom: 14px;
  margin-left: 14px;
  width: 210px;
  height: 140px;
  overflow: hidden; }

.lz-atlas-preview .lz-atlas-ul li a img {
  float: left;
  width: 210px;
  height: 140px;
  border: none;
  object-fit: cover; }

.lz-atlas-preview.lz-atlas-ul li a img:hover {
  border-top-width: 5px;
  border-top-style: solid;
  border-top-color: #3d85c6;
  border-right-width: 5px;
  border-right-style: solid;
  border-right-color: #3d85c6;
  border-bottom-width: 5px;
  border-bottom-style: solid;
  border-bottom-color: #3d85c6;
  border-left-width: 5px;
  border-left-style: solid;
  border-left-color: #3d85c6; }

.lz-atlas-preview.lz-atlas-ul li a h3 {
  /*-position-*/
  position: absolute;
  z-index: 8;
  /*-margin-*/
  padding: 0 10px;
  margin: 0;
  /*-font-*/
  font-size: 14px;
  text-align: center;
  font-weight: 400;
  font-style: normal;
  font-family: 'Microsoft YaHei';
  color: white;
  line-height: 22px;
  /*-background-*/
  background-color: rgba(0, 0, 0, 0.6);
  background-image: none;
  background-image: linear-gradient(top, none, none);
  background-repeat: repeat;
  background-position: 0 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #333 \0;
  *background: #333; }

.lz-atlas-preview .lz-colseBut {
  position: absolute;
  width: 60px;
  height: 60px;
  right: 10px;
  top: 10px;
  z-index: 1000;
  /*font*/
  line-height: 60px;
  font-weight: 100;
  text-decoration: none;
  color: #fff;
  opacity: .3;
  font-size: 30px;
  display: block;
  /*border Radius*/
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 50%;
  box-sizing: content-box; }

.lz-atlas-preview .lz-colseBut .lz-itemicon {
  font-size: 35px; }

.lz-atlas-preview .lz-colseBut:hover {
  color: #fff;
  opacity: 1; }

.lz-atlas-preview.lz-atlas-mod {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999999;
  text-align: center;
  background: rgba(0, 0, 0, 0.9);
  *background: #000;
  background: #000 \0; }

.lz-atlas-preview .lz-display {
  width: 100%;
  height: 100%; }

.lz-atlas-preview .w_display, .lz-thumbnail-box {
  width: 100%; }

.lz-atlas-preview .lz-atlas-box {
  width: 100%;
  height: 100%;
  text-align: center;
  margin: 0 auto;
  position: relative;
  margin-bottom: 10px !important; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd {
  width: 100%;
  height: 100%;
  z-index: 300;
  position: relative; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd ul {
  list-style: none;
  margin: 0;
  width: 540px; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-tempWrap {
  width: 100%;
  height: 100%; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child {
  position: absolute;
  left: 0;
  top: 34px;
  bottom: 128px;
  overflow: hidden; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child li {
  overflow: hidden;
  float: left; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child li a {
  display: block;
  max-width: 80%;
  height: 100%;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  cursor: default; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child li a:active {
  cursor: move; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child li a .lz-reference {
  height: 100%;
  display: inline-block;
  *display: inline;
  zoom: 1;
  vertical-align: middle; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-tempWrap ul:first-child li a img {
  max-width: 99%;
  max-height: 100%;
  vertical-align: middle; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-img-but {
  position: absolute;
  width: 100%;
  z-index: 999; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-img-but a {
  position: absolute;
  z-index: 99;
  text-decoration: none;
  color: #fff;
  opacity: .3;
  width: 10%;
  float: left;
  line-height: 540px; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-se-but a {
  width: 30px; }

.lz-atlas-preview .lz-img-bd .lz-img-but a:hover {
  color: #fff;
  opacity: 0.7; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-img-but a .lz-itemicon {
  font-size: 45px; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-nextBut {
  text-align: left;
  position: relative;
  left: 60px; }

.lz-atlas-preview .lz-atlas-box .lz-img-bd .lz-previousBut {
  text-align: right;
  position: relative;
  right: 60px; }

.lz-atlas-preview .lz-nextBut-se.disabled,
.lz-atlas-preview .lz-previousBut-se.disabled,
.lz-atlas-preview .lz-previousBut.disabled,
.lz-atlas-preview .lz-nextBut.disabled {
  color: #ccc !important;
  opacity: .1 !important;
  cursor: default; }

.lz-atlas-preview .lz-thumbnail-box {
  text-align: center;
  position: absolute;
  left: 0;
  bottom: 34px;
  z-index: 1000; }

.lz-atlas-preview .lz-thumbnail-box .lz-tempWrap {
  width: 670px;
  margin: 0 auto;
  position: relative;
  overflow: hidden; }

.lz-atlas-preview .lz-thumbnail-box .lz-img-but {
  width: 100%;
  position: absolute;
  top: 50%;
  margin-top: -30px; }

.lz-atlas-preview .lz-thumbnail-box ul {
  height: 64px;
  padding: 0;
  list-style: none;
  overflow: hidden; }

.lz-atlas-preview .lz-thumbnail-box ul li {
  float: left;
  margin-bottom: 10px; }

.lz-atlas-preview .lz-thumbnail-box ul li a {
  width: 59px;
  height: 59px;
  border: 2px solid #222;
  float: left;
  position: relative;
  margin: 0 2px;
  background: #111; }

.lz-atlas-preview .lz-thumbnail-box ul li a:hover {
  border: 2px solid #fff; }

.lz-atlas-preview .lz-thumbnail-box .lz-action a {
  border: 2px solid #fff; }

.lz-atlas-preview .lz-thumbnail-box ul li a img {
  max-width: 100%;
  max-height: 80px;
  vertical-align: middle; }

.lz-atlas-preview .lz-thumbnail-box .lz-reference {
  height: 59px;
  display: inline-block;
  *display: inline;
  zoom: 1;
  vertical-align: middle; }

.lz-atlas-preview .lz-thumbnail-box .lz-img-bd {
  margin: 0 auto !important;
  width: 780px;
  padding: 0;
  position: relative; }

.lz-atlas-preview .lz-btn-pointer {
  margin: 0 auto;
  width: 670px;
  overflow: hidden; }

.lz-atlas-preview .lz-thumbnail-box .lz-img-but .lz-but {
  line-height: 60px;
  color: #999;
  text-decoration: none;
  position: absolute;
  height: 60px;
  width: 40px;
  text-decoration: none;
  color: #444; }

.lz-atlas-preview .lz-thumbnail-box .lz-img-but .lz-but .lz-itemicon {
  font-size: 24px; }

.lz-atlas-preview .lz-thumbnail-box .lz-img-but .lz-previousBut {
  top: 0;
  right: 10px; }

.lz-atlas-preview .lz-thumbnail-box .lz-img-but .lz-nextBut {
  top: 0;
  left: 10px; }

.lz-atlas-preview .lz-se-but:hover {
  color: #fff; }

.lz-atlas-preview .lz-nextBut-se {
  margin-left: -10px; }

.lz-atlas-preview .lz-previousBut-se {
  margin-left: 715px; }

.lz-atlas-preview .lz-see-change {
  width: 120px;
  padding: 10px 12px;
  background-color: rgba(0, 0, 0, 0.8);
  color: rgba(255, 255, 255, 0.56);
  color: #fff\0;
  *color: #fff;
  background: #111 \0;
  *background: #111;
  overflow: hidden;
  position: fixed;
  bottom: 132px;
  left: 50%;
  margin-left: -72px;
  z-index: 310;
  border-radius: 4px; }

.lz-atlas-preview .lz-see-change-sm,
.lz-atlas-preview .lz-see-change-lg {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.56);
  color: #fff\0;
  *color: #fff; }

.lz-atlas-preview .lz-see-change-sm {
  float: left; }

.lz-atlas-preview .lz-see-change-lg {
  float: right; }

.lz-atlas-preview .lz-see-change-txt {
  display: block;
  overflow: hidden;
  color: #fff\0;
  *color: #fff; }

.lz-atlas-preview .lz-see-change-sm:hover,
.lz-atlas-preview .lz-see-change-lg:hover {
  color: #fff; }

/*# sourceMappingURL=atlas-preview.css.map */

.lz-inner-img-box-size{
    width: 100% !important;
}
.lz-inner-img-size {
    width: 100% !important;
    height: 100% !important;
}