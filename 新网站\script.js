// 版本7 - 企业官网风格脚本
document.addEventListener('DOMContentLoaded', function() {
    initNavigation();
    initBannerSlider();
    initScrollEffects();
    initBackToTop();
    initAnimations();
    initSearch();
});

// 导航功能
function initNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('section[id]');
    
    // 平滑滚动导航
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // 如果是锚点链接
            if (href.startsWith('#')) {
                e.preventDefault();
                
                const targetSection = document.querySelector(href);
                if (targetSection) {
                    const offsetTop = targetSection.offsetTop - 80;
                    
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                    
                    // 更新活动状态
                    updateActiveNavLink(href);
                }
            }
        });
    });
    
    // 滚动时更新导航状态
    window.addEventListener('scroll', throttle(updateNavOnScroll, 100));
    
    function updateNavOnScroll() {
        const scrollPosition = window.scrollY + 150;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            const sectionId = '#' + section.getAttribute('id');
            
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                updateActiveNavLink(sectionId);
            }
        });
    }
    
    function updateActiveNavLink(activeId) {
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === activeId) {
                link.classList.add('active');
            }
        });
    }
}

// 横幅轮播
function initBannerSlider() {
    const slides = document.querySelectorAll('.slide');
    const indicators = document.querySelectorAll('.indicator');
    const prevBtn = document.querySelector('.arrow-prev');
    const nextBtn = document.querySelector('.arrow-next');
    
    let currentSlide = 0;
    let slideInterval;
    
    // 显示指定幻灯片
    function showSlide(index) {
        slides.forEach((slide, i) => {
            slide.classList.toggle('active', i === index);
        });
        
        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('active', i === index);
        });
        
        currentSlide = index;
    }
    
    // 下一张幻灯片
    function nextSlide() {
        const next = (currentSlide + 1) % slides.length;
        showSlide(next);
    }
    
    // 上一张幻灯片
    function prevSlide() {
        const prev = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(prev);
    }
    
    // 自动播放
    function startAutoPlay() {
        slideInterval = setInterval(nextSlide, 5000);
    }
    
    function stopAutoPlay() {
        clearInterval(slideInterval);
    }
    
    // 事件监听
    nextBtn.addEventListener('click', () => {
        nextSlide();
        stopAutoPlay();
        startAutoPlay();
    });
    
    prevBtn.addEventListener('click', () => {
        prevSlide();
        stopAutoPlay();
        startAutoPlay();
    });
    
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            showSlide(index);
            stopAutoPlay();
            startAutoPlay();
        });
    });
    
    // 鼠标悬停暂停自动播放
    const bannerContainer = document.querySelector('.banner-container');
    bannerContainer.addEventListener('mouseenter', stopAutoPlay);
    bannerContainer.addEventListener('mouseleave', startAutoPlay);
    
    // 启动自动播放
    startAutoPlay();
}

// 滚动效果
function initScrollEffects() {
    const navbar = document.querySelector('.main-nav');
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', throttle(() => {
        const currentScrollY = window.scrollY;
        
        // 导航栏阴影效果
        if (currentScrollY > 50) {
            navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.boxShadow = '0 1px 2px 0 rgba(0, 0, 0, 0.05)';
        }
        
        lastScrollY = currentScrollY;
    }, 100));
}

// 返回顶部按钮
function initBackToTop() {
    const backToTopBtn = document.querySelector('.back-to-top');
    
    window.addEventListener('scroll', throttle(() => {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('show');
        } else {
            backToTopBtn.classList.remove('show');
        }
    }, 100));
    
    backToTopBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// 滚动动画
function initAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
                
                // 数字计数动画
                if (entry.target.classList.contains('stat-number')) {
                    animateNumber(entry.target);
                }
                
                // 只观察一次
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animateElements = document.querySelectorAll(`
        .service-card,
        .case-card,
        .stat-card,
        .contact-item,
        .text-block
    `);
    
    animateElements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(el);
    });
    
    // 观察数字元素
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(el => {
        observer.observe(el);
    });
}

// 数字计数动画
function animateNumber(element) {
    const text = element.textContent;
    const number = parseInt(text.match(/\d+/)[0]);
    const suffix = text.replace(/\d+/, '');
    const duration = 2000;
    const step = number / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += step;
        if (current >= number) {
            current = number;
            clearInterval(timer);
        }
        
        element.textContent = Math.floor(current) + suffix;
    }, 16);
}

// 搜索功能
function initSearch() {
    const searchInput = document.querySelector('.search-box input');
    const searchBtn = document.querySelector('.search-box button');
    
    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            // 这里可以实现真实的搜索功能
            console.log('搜索:', query);
            alert(`搜索功能开发中，搜索关键词：${query}`);
        }
    }
    
    searchBtn.addEventListener('click', performSearch);
    
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
}

// 下拉菜单增强
function initDropdownMenus() {
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.dropdown-menu');
        let timeout;
        
        dropdown.addEventListener('mouseenter', () => {
            clearTimeout(timeout);
            menu.style.opacity = '1';
            menu.style.visibility = 'visible';
            menu.style.transform = 'translateY(0)';
        });
        
        dropdown.addEventListener('mouseleave', () => {
            timeout = setTimeout(() => {
                menu.style.opacity = '0';
                menu.style.visibility = 'hidden';
                menu.style.transform = 'translateY(-10px)';
            }, 100);
        });
    });
}

// 表单处理
function initFormHandling() {
    const formBtns = document.querySelectorAll('.form-btn');
    
    formBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            // 添加点击统计
            console.log('表单按钮点击');
            
            // 可以添加表单提交前的验证
            // e.preventDefault();
        });
    });
}

// 联系方式点击统计
function initContactTracking() {
    const phoneLinks = document.querySelectorAll('a[href^="tel:"]');
    const emailLinks = document.querySelectorAll('a[href^="mailto:"]');
    
    phoneLinks.forEach(link => {
        link.addEventListener('click', () => {
            console.log('电话点击:', link.href);
        });
    });
    
    emailLinks.forEach(link => {
        link.addEventListener('click', () => {
            console.log('邮箱点击:', link.href);
        });
    });
}

// 页面可见性API
function initVisibilityAPI() {
    document.addEventListener('visibilitychange', () => {
        const bannerContainer = document.querySelector('.banner-container');
        
        if (document.hidden) {
            // 页面隐藏时暂停轮播
            bannerContainer.style.animationPlayState = 'paused';
        } else {
            // 页面显示时恢复轮播
            bannerContainer.style.animationPlayState = 'running';
        }
    });
}

// 键盘导航支持
function initKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
        // ESC键返回顶部
        if (e.key === 'Escape') {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // 左右箭头控制轮播
        if (e.key === 'ArrowLeft') {
            document.querySelector('.arrow-prev').click();
        } else if (e.key === 'ArrowRight') {
            document.querySelector('.arrow-next').click();
        }
    });
}

// 工具函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 初始化所有增强功能
function initEnhancedFeatures() {
    initDropdownMenus();
    initFormHandling();
    initContactTracking();
    initVisibilityAPI();
    initKeyboardNavigation();
}

// 页面加载完成后初始化增强功能
window.addEventListener('load', initEnhancedFeatures);

// 添加CSS动画类
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        opacity: 1 !important;
        transform: translateY(0) !important;
    }
    
    /* 平滑的过渡效果 */
    .service-card,
    .case-card,
    .stat-card {
        transition: all 0.3s ease;
    }
    
    /* 焦点样式 */
    *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    /* 减少动画模式支持 */
    @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
`;
document.head.appendChild(style);
