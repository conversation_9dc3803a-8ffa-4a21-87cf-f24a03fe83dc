document.write('\r\n\r\n\r\n        \u003cmeta name=\"viewport\" content=\"width=device-width\" /\u003e\r\n\r\n    \u003cmeta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" /\u003e\r\n    \u003cmeta name=\"description\" /\u003e\r\n    \u003cmeta name=\"keywords\" content=\"AIBOX EdgeBOX 边缘计算 边缘网关 边缘设备\" /\u003e\r\n    \u003cmeta name=\"renderer\" content=\"webkit\" /\u003e\r\n    \u003cmeta name=\"applicable-device\" content=\"pc\" /\u003e\r\n    \u003cmeta http-equiv=\"Cache-Control\" content=\"no-transform\" /\u003e\r\n    \u003ctitle\u003e上海边一科技有限公司 \u003c/title\u003e\r\n    \u003clink rel=\"icon\" href=\"../../../../../../../sitefiles10197/10197361/1591256124(1).png\"/\u003e\u003clink rel=\"shortcut icon\" href=\"../../../../../../../sitefiles10197/10197361/1591256124(1).png\"/\u003e\u003clink rel=\"bookmark\" href=\"../../../../../../../sitefiles10197/10197361/1591256124(1).png\"/\u003e\r\n    \u003clink href=\"../../../../../../../../Designer/Content/bottom/pcstyle.css?_version=20230608105041\" rel=\"stylesheet\" type=\"text/css\"/\u003e\r\n    \u003clink href=\"../../../../../../../Content/public/css/reset.css?_version=20230608105041\" rel=\"stylesheet\" type=\"text/css\"/\u003e\r\n    \u003clink href=\"../../../../../../../static/iconfont/1.0.0/iconfont.css?_version=20230608105045\" rel=\"stylesheet\" type=\"text/css\"/\u003e\r\n    \u003clink href=\"../../../../../../../static/iconfont/designer/iconfont.css?_version=20230629105945\" rel=\"stylesheet\" type=\"text/css\"/\u003e\r\n    \u003clink href=\"../../../../../../../static/iconfont/companyinfo/iconfont.css?_version=20230608105045\" rel=\"stylesheet\" type=\"text/css\"/\u003e\r\n    \u003clink href=\"../../../../../../../Designer/Content/base/css/pager.css?_version=20230608105041\" rel=\"stylesheet\" type=\"text/css\"/\u003e\r\n    \u003clink href=\"../../../../../../../Designer/Content/base/css/hover-effects.css?_version=20230608105041\" rel=\"stylesheet\" type=\"text/css\"/\u003e\r\n    \u003clink href=\"../../../../../../../Designer/Content/base/css/antChain.css?_version=20230608105041\" rel=\"stylesheet\" type=\"text/css\"/\u003e\r\n\r\n\r\n    \r\n    \u003clink href=\"../../../../../../../pubsf/10197/10197361/css/60150_Pc_zh-CN.css?preventCdnCacheSeed=4381bb0dcb0c419db6a82476bc0db30b\" rel=\"stylesheet\" /\u003e\r\n    \u003cscript src=\"../../../../../../../Scripts/JQuery/jquery-3.6.3.min.js?_version=20230608105042\" type=\"text/javascript\"\u003e\u003c/script\u003e\r\n    \u003cscript src=\"../../../../../../../Designer/Scripts/jquery.lazyload.min.js?_version=20230608105042\" type=\"text/javascript\"\u003e\u003c/script\u003e\r\n    \u003cscript src=\"../../../../../../../Designer/Scripts/smart.animation.min.js?_version=20230608105042\" type=\"text/javascript\"\u003e\u003c/script\u003e\r\n    \u003cscript src=\"../../../../../../../Designer/Content/Designer-panel/js/kino.razor.min.js?_version=20230608105041\" type=\"text/javascript\"\u003e\u003c/script\u003e\r\n    \u003cscript src=\"../../../../../../../Scripts/common.min.js?v=20200318\u0026_version=20230608105042\" type=\"text/javascript\"\u003e\u003c/script\u003e\r\n    \u003cscript src=\"../../../../../../../Administration/Scripts/admin.validator.min.js?_version=20230608105035\" type=\"text/javascript\"\u003e\u003c/script\u003e\r\n    \u003cscript src=\"../../../../../../../Administration/Content/plugins/cookie/jquery.cookie.js?_version=20230608105034\" type=\"text/javascript\"\u003e\u003c/script\u003e\r\n\r\n    \u003cscript type=\u0027text/javascript\u0027 id=\u0027jssor-all\u0027 src=\u0027../../../../../../../Designer/Scripts/jssor.slider-22.2.16-all.min.js?_version=20230608105042\u0027 \u003e\u003c/script\u003e\u003cscript type=\u0027text/javascript\u0027 id=\u0027slideshown\u0027 src=\u0027../../../../../../../Designer/Scripts/slideshow.js?_version=20230608105042\u0027 \u003e\u003c/script\u003e\u003cscript type=\u0027text/javascript\u0027 id=\u0027lzparallax\u0027 src=\u0027../../../../../../../static/lzparallax/1.0.0/lz-parallax.min.js\u0027 \u003e\u003c/script\u003e\u003cscript type=\u0027text/javascript\u0027 id=\u0027SuperSlide\u0027 src=\u0027../../../../../../../Designer/Content/Designer-panel/js/jquery.SuperSlide.2.1.1.js\u0027 \u003e\u003c/script\u003e\u003cscript type=\u0027text/javascript\u0027 id=\u0027jqPaginator\u0027 src=\u0027../../../../../../../Scripts/statics/js/jqPaginator.min.js\u0027 \u003e\u003c/script\u003e\u003cscript type=\u0027text/javascript\u0027 id=\u0027lz-slider\u0027 src=\u0027../../../../../../../Scripts/statics/js/lz-slider.min.js\u0027 \u003e\u003c/script\u003e\u003cscript type=\u0027text/javascript\u0027 id=\u0027lz-preview\u0027 src=\u0027../../../../../../../Scripts/statics/js/lz-preview.min.js\u0027 \u003e\u003c/script\u003e\r\n    \r\n    \u003cscript type=\"text/javascript\"\u003e\r\n        $.ajaxSetup({\r\n            cache: false,\r\n            beforeSend: function (jqXHR, settings) {\r\n                settings.data = settings.data \u0026\u0026 settings.data.length \u003e 0 ? (settings.data + \"\u0026\") : \"\";\r\n                settings.data = settings.data + \"__RequestVerificationToken=\" + $(\u0027input[name=\"__RequestVerificationToken\"]\u0027).val();\r\n                return true;\r\n            }\r\n        });\r\n    \u003c/script\u003e\r\n');